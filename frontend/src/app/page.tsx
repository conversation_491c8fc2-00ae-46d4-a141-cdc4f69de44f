// Main Game Page - Session selection and game interface

'use client';

import React from 'react';
import { GameLayout } from '@/components/layout/GameLayout';
import { SessionManager } from '@/components/session/SessionManager';
import { useGameStore } from '@/stores/gameStore';
import { GameSession } from '@/types';

export default function Home() {
  const { currentSession, setCurrentSession } = useGameStore();

  const handleSessionSelect = (session: GameSession) => {
    setCurrentSession(session);
  };

  // If no session is active, show session manager
  if (!currentSession) {
    return (
      <GameLayout>
        <SessionManager onSessionSelect={handleSessionSelect} />
      </GameLayout>
    );
  }

  // If session is active, let MainContent handle panel navigation
  return (
    <GameLayout>
      <div className="h-full flex items-center justify-center">
        <p className="text-muted-foreground">Game session is active. Use the panels to interact with your game.</p>
      </div>
    </GameLayout>
  );
}
